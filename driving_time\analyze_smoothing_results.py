"""
分析速度平滑处理结果
"""

import pandas as pd
import numpy as np

def analyze_smoothing_results():
    """分析平滑处理结果"""
    print("=== 速度平滑处理结果分析 ===\n")
    
    # 读取处理后的数据
    df = pd.read_csv('smoothed_drivingtime.csv')
    
    print(f"数据总量: {len(df)} 条记录")
    print(f"时间范围: {df['gps_time'].min()} 到 {df['gps_time'].max()}")
    
    # 基本统计信息
    print("\n--- 速度统计信息 ---")
    print("原始速度:")
    print(f"  平均值: {df['original_speed'].mean():.2f} km/h")
    print(f"  标准差: {df['original_speed'].std():.2f} km/h")
    print(f"  最小值: {df['original_speed'].min():.2f} km/h")
    print(f"  最大值: {df['original_speed'].max():.2f} km/h")
    
    print("\n平滑后速度:")
    print(f"  平均值: {df['smoothed_speed'].mean():.2f} km/h")
    print(f"  标准差: {df['smoothed_speed'].std():.2f} km/h")
    print(f"  最小值: {df['smoothed_speed'].min():.2f} km/h")
    print(f"  最大值: {df['smoothed_speed'].max():.2f} km/h")
    
    # 速度变化分析
    print("\n--- 速度变化分析 ---")
    original_diff = np.abs(np.diff(df['original_speed']))
    smoothed_diff = np.abs(np.diff(df['smoothed_speed']))
    
    print("原始速度变化:")
    print(f"  平均变化率: {original_diff.mean():.2f} km/h")
    print(f"  最大变化: {original_diff.max():.2f} km/h")
    print(f"  变化>10km/h的次数: {np.sum(original_diff > 10)}")
    
    print("\n平滑后速度变化:")
    print(f"  平均变化率: {smoothed_diff.mean():.2f} km/h")
    print(f"  最大变化: {smoothed_diff.max():.2f} km/h")
    print(f"  变化>10km/h的次数: {np.sum(smoothed_diff > 10)}")
    
    # 平滑效果评估
    improvement = (original_diff.mean() - smoothed_diff.mean()) / original_diff.mean() * 100
    print(f"\n平滑度改善: {improvement:.1f}%")
    
    # 速度差值分析
    print("\n--- 速度差值分析 ---")
    speed_diff = df['speed_difference']
    print(f"平均差值: {speed_diff.mean():.3f} km/h")
    print(f"差值标准差: {speed_diff.std():.3f} km/h")
    print(f"最大正差值: {speed_diff.max():.3f} km/h")
    print(f"最大负差值: {speed_diff.min():.3f} km/h")
    
    # 找出速度突变点
    print("\n--- 速度突变点检测 ---")
    large_changes = np.where(original_diff > 20)[0]
    if len(large_changes) > 0:
        print(f"检测到 {len(large_changes)} 个大幅速度变化点 (>20km/h)")
        for i in large_changes[:5]:  # 显示前5个
            print(f"  位置 {i}: {df.iloc[i]['original_speed']:.1f} -> {df.iloc[i+1]['original_speed']:.1f} km/h")
            print(f"    平滑后: {df.iloc[i]['smoothed_speed']:.1f} -> {df.iloc[i+1]['smoothed_speed']:.1f} km/h")
    else:
        print("未检测到大幅速度变化点")
    
    # 停车和行驶状态分析
    print("\n--- 停车/行驶状态分析 ---")
    original_stopped = np.sum(df['original_speed'] == 0)
    smoothed_stopped = np.sum(df['smoothed_speed'] < 1)  # 考虑平滑后的微小值
    
    print(f"原始数据停车点: {original_stopped} 个 ({original_stopped/len(df)*100:.1f}%)")
    print(f"平滑后低速点(<1km/h): {smoothed_stopped} 个 ({smoothed_stopped/len(df)*100:.1f}%)")
    
    # 速度区间分析
    print("\n--- 速度区间分析 ---")
    speed_ranges = [(0, 10), (10, 30), (30, 60), (60, 90)]
    
    for low, high in speed_ranges:
        original_count = np.sum((df['original_speed'] >= low) & (df['original_speed'] < high))
        smoothed_count = np.sum((df['smoothed_speed'] >= low) & (df['smoothed_speed'] < high))
        print(f"{low}-{high}km/h: 原始 {original_count} 个, 平滑后 {smoothed_count} 个")

def find_problematic_segments():
    """找出可能有问题的轨迹段"""
    print("\n=== 问题轨迹段检测 ===\n")
    
    df = pd.read_csv('smoothed_drivingtime.csv')
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    
    # 检测异常速度跳跃
    speed_diff = np.abs(np.diff(df['original_speed']))
    problematic_indices = np.where(speed_diff > 30)[0]
    
    if len(problematic_indices) > 0:
        print(f"发现 {len(problematic_indices)} 个异常速度跳跃点:")
        for idx in problematic_indices[:10]:  # 显示前10个
            before = df.iloc[idx]
            after = df.iloc[idx + 1]
            time_diff = (after['gps_time'] - before['gps_time']).total_seconds()
            
            print(f"\n时间: {before['gps_time']} -> {after['gps_time']}")
            print(f"时间间隔: {time_diff:.0f}秒")
            print(f"速度变化: {before['original_speed']:.1f} -> {after['original_speed']:.1f} km/h")
            print(f"平滑后: {before['smoothed_speed']:.1f} -> {after['smoothed_speed']:.1f} km/h")
            print(f"位置: ({before['latitude']:.6f}, {before['longitude']:.6f}) -> ({after['latitude']:.6f}, {after['longitude']:.6f})")
    else:
        print("未发现明显的异常速度跳跃")

if __name__ == "__main__":
    analyze_smoothing_results()
    find_problematic_segments()

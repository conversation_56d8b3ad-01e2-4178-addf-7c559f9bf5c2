import datetime
from itertools import groupby
from itertools import tee
import pandas as pd

files = ['X:\\track_report\\driving_time\\广西超时超速\\桂K66936车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KC4469车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KH3499车辆20250101-0624.csv']

for file in files:
    df = pd.read_csv(file,engine='pyarrow')
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    df = df.sort_values(by='gps_time')

    
    total_driving_time = 0
    driver = {}
    for i, j in groupby(df.iterrows(), key=lambda row: (row[1]['speed'] if row[1]['speed'] <= 100 else 1)):
        itorMax, itorMin = tee(j, 2)
        # str to datetime
        
        maxItem = max(itorMax, key=lambda row: row[1]['gps_time'])
        minItem = min(itorMin, key=lambda row: row[1]['gps_time'])
        maxTime = maxItem[1]['gps_time']
        minTime = minItem[1]['gps_time']
        vehicleNo = maxItem[1]['vehicle_no']
        maxTimeStr = maxTime.strftime("%Y-%m-%d %H:%M:%S.000")
        minTimeStr = minTime.strftime("%Y-%m-%d %H:%M:%S.000")
        # maxTime = datetime.datetime.strptime(maxTimeStr, "%Y-%m-%d %H:%M:%S.000")
        # minTime = datetime.datetime.strptime(minTimeStr, "%Y-%m-%d %H:%M:%S.000")
        if i == 1:
            print(f"{vehicleNo} 超速时间段：开始时间：{minTimeStr}，结束时间：{maxTimeStr}，持续时长：{maxTime - minTime}")
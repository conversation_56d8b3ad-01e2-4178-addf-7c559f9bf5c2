import datetime
from itertools import groupby
from itertools import tee
import pandas as pd

files = ['X:\\track_report\\driving_time\\广西超时超速\\桂K66936车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KC4469车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KH3499车辆20250101-0624.csv']

for file in files:
    df = pd.read_csv(file,engine='pyarrow')
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    df = df.sort_values(by='gps_time')
    total_driving_time = 0
    total_stop_time = 0
    driver = {}
    startDrivingTime = None
    StopDrivingTime = None
    for i, j in groupby(df.iterrows(), key=lambda row: (row[1]['speed'] if row[1]['speed'] == 0 else 1)):
        itorMax, itorMin = tee(j, 2)
        # str to datetime
        
        maxItem = max(itorMax, key=lambda row: row[1]['gps_time'])
        minItem = min(itorMin, key=lambda row: row[1]['gps_time'])
        maxTime = maxItem[1]['gps_time']
        minTime = minItem[1]['gps_time']
        maxTimeStr = maxTime.strftime("%Y-%m-%d %H:%M:%S.000")
        minTimeStr = minTime.strftime("%Y-%m-%d %H:%M:%S.000")
        driverId = maxItem[1]['vehicle_no']
        if i == 1:
            if startDrivingTime is None:
                startDrivingTime = minTimeStr
            stopDrivingTime = maxTimeStr
            if maxTime - minTime > datetime.timedelta(hours=4):
                total_stop_time += ((maxTime - minTime).seconds)
            else:
            #print(f"{driverId} 行驶时间段：开始时间：{minTimeStr}，结束时间：{maxTimeStr}，持续时长：{maxTime - minTime}")
                total_driving_time += ((maxTime - minTime).seconds)
        else:
            total_stop_time += ((maxTime - minTime).seconds)

        if total_stop_time > 1200:
            if total_driving_time > 4*60*60:
                print(f"{driverId} 驾驶超时 开始时间{startDrivingTime} 结束时间{stopDrivingTime} 持续时长{(total_driving_time) / 3600:.2f}\t")
                # total_stop_time = 0
                # total_driving_time = 0
                # startDrivingTime = None
                # stopDrivingTime = None
            total_driving_time = 0
            total_stop_time = 0
            startDrivingTime = None
            stopDrivingTime = None
            #print(f"{driverId} 停车时长足够了，可以清除驾驶时间")
            
